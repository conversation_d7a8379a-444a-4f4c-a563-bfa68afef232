import json
import requests
import time
import hmac
import hashlib
import urllib.parse
api_key = "Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW"
secret_key = "eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh"
base_url = "https://api.binance.com"  # 现货接口地址
# 生成签名
timestamp = int(time.time() * 1000)
params = {
    'timestamp': timestamp
}
query_string = urllib.parse.urlencode(params)
signature = hmac.new(secret_key.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()
# 添加签名到参数
params['signature'] = signature
# 发送请求
headers = {
    'X-MBX-APIKEY': api_key
}
response = requests.get(f'{base_url}/api/v3/account', headers=headers, params=params)

print(json.dumps(response.json()))